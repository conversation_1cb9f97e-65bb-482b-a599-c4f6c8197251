package dev.pigmomo.yhkit2025.utils

import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringOperationType
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringType
import dev.pigmomo.yhkit2025.utils.productmonitor.MonitoringStatusUtils
import org.junit.Test
import org.junit.Assert.*
import java.util.*

/**
 * 监控状态工具类测试
 */
class MonitoringStatusUtilsTest {

    @Test
    fun `测试间隔监控状态计算_首次执行`() {
        val plan = createTestPlan(
            operationType = MonitoringOperationType.INTERVAL,
            intervalSeconds = 60,
            lastExecutedAt = null
        )
        
        val status = MonitoringStatusUtils.getMonitoringStatus(plan, isSchedulerRunning = true)
        
        assertTrue("首次执行应该准备执行", status.statusText.contains("准备执行") || status.statusText.contains("等待条件满足"))
    }

    @Test
    fun `测试间隔监控状态计算_等待中`() {
        val now = Date()
        val lastExecuted = Date(now.time - 30 * 1000) // 30秒前执行
        
        val plan = createTestPlan(
            operationType = MonitoringOperationType.INTERVAL,
            intervalSeconds = 60,
            lastExecutedAt = lastExecuted
        )
        
        val status = MonitoringStatusUtils.getMonitoringStatus(plan, currentTime = now, isSchedulerRunning = true)
        
        assertTrue("应该显示剩余时间", status.isWaiting)
        assertTrue("剩余时间应该约为30秒", status.remainingSeconds in 25..35)
        assertTrue("状态文本应该包含秒数", status.statusText.contains("秒后执行"))
    }

    @Test
    fun `测试间隔监控状态计算_时间到达`() {
        val now = Date()
        val lastExecuted = Date(now.time - 70 * 1000) // 70秒前执行，超过60秒间隔
        
        val plan = createTestPlan(
            operationType = MonitoringOperationType.INTERVAL,
            intervalSeconds = 60,
            lastExecutedAt = lastExecuted
        )
        
        val status = MonitoringStatusUtils.getMonitoringStatus(plan, currentTime = now, isSchedulerRunning = true)
        
        assertFalse("时间到达时不应该等待", status.isWaiting)
        assertEquals("剩余时间应该为0", 0, status.remainingSeconds)
        assertTrue("状态应该是准备执行", status.statusText.contains("准备执行"))
    }

    @Test
    fun `测试调度器停止状态`() {
        val plan = createTestPlan(
            operationType = MonitoringOperationType.INTERVAL,
            intervalSeconds = 60
        )
        
        val status = MonitoringStatusUtils.getMonitoringStatus(plan, isSchedulerRunning = false)
        
        assertFalse("调度器停止时不应该等待", status.isWaiting)
        assertTrue("状态应该显示调度器已停止", status.statusText.contains("调度器已停止"))
    }

    @Test
    fun `测试禁用状态`() {
        val plan = createTestPlan(
            operationType = MonitoringOperationType.INTERVAL,
            intervalSeconds = 60,
            isEnabled = false
        )
        
        val status = MonitoringStatusUtils.getMonitoringStatus(plan, isSchedulerRunning = true)
        
        assertFalse("禁用时不应该等待", status.isWaiting)
        assertTrue("状态应该显示已禁用", status.statusText.contains("已禁用"))
    }

    @Test
    fun `测试执行次数限制`() {
        val plan = createTestPlan(
            operationType = MonitoringOperationType.INTERVAL,
            intervalSeconds = 60,
            maxExecutions = 5,
            executedCount = 5
        )
        
        val status = MonitoringStatusUtils.getMonitoringStatus(plan, isSchedulerRunning = true)
        
        assertFalse("达到执行次数限制时不应该等待", status.isWaiting)
        assertTrue("状态应该显示已完成", status.statusText.contains("已完成"))
    }

    @Test
    fun `测试手动执行状态`() {
        val plan = createTestPlan(
            operationType = MonitoringOperationType.MANUAL,
            intervalSeconds = 60
        )
        
        val status = MonitoringStatusUtils.getMonitoringStatus(plan, isSchedulerRunning = true)
        
        assertFalse("手动执行不应该等待", status.isWaiting)
        assertTrue("状态应该显示手动执行", status.statusText.contains("手动执行"))
    }

    private fun createTestPlan(
        operationType: MonitoringOperationType,
        intervalSeconds: Int,
        lastExecutedAt: Date? = null,
        isEnabled: Boolean = true,
        maxExecutions: Int = -1,
        executedCount: Int = 0
    ): MonitoringPlanEntity {
        return MonitoringPlanEntity(
            id = 1,
            name = "测试计划",
            type = MonitoringType.PRODUCT,
            operationType = operationType,
            intervalSeconds = intervalSeconds,
            scheduledTime = null,
            productIds = listOf("123"),
            isEnabled = isEnabled,
            priority = 1,
            maxExecutions = maxExecutions,
            executedCount = executedCount,
            lastExecutedAt = lastExecutedAt,
            createdAt = Date(),
            updatedAt = Date(),
            startTime = null,
            endTime = null
        )
    }
}
